import axios from 'axios';
import { URL_API } from '.';

export const apiCorrectiveMaintenance = axios.create({
  baseURL: URL_API + '/vendor-platform/corrective-maintenance',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Types for Corrective Maintenance
export interface CorrectiveMaintenanceOrder {
  _id: string;
  stockId: string;
  associateId: string;
  workshopId: string;
  type: 'customer-initiated' | 'preventive-detected';
  status: 'pending' | 'diagnosed' | 'quoted' | 'approved' | 'in-progress' | 'completed' | 'cancelled';
  failureType: 'known' | 'unknown';
  arrivalMethod: 'driving' | 'tow-truck';
  customerDescription: string;
  canVehicleDrive: boolean;
  needsTowTruck: boolean;
  approvalType: 'fleet' | 'customer';
  totalEstimatedCost: number;
  slaTarget: string;
  createdAt: string;
  updatedAt: string;
  vehicle?: {
    _id: string;
    brand: string;
    model: string;
    carNumber: string;
    carPlates: {
      plates: string;
    };
  };
  workshop?: {
    _id: string;
    name: string;
  };
  diagnosis?: Diagnosis;
  quotation?: Quotation;
  services?: CorrectiveService[];
}

export interface CorrectiveService {
  _id: string;
  orderId: string;
  serviceType: string;
  serviceName: string;
  description: string;
  status: 'not-started' | 'in-progress' | 'completed' | 'waiting-for-parts' | 'cancelled';
  estimatedCost: number;
  laborCost: number;
  estimatedDuration: number;
  slaTarget: string;
  parts: ServicePart[];
  evidence: Evidence[];
  createdAt: string;
  updatedAt: string;
}

export interface ServicePart {
  name: string;
  quantity: number;
  unitCost: number;
  totalCost: number;
  supplier?: string;
  partNumber?: string;
  availability: 'available' | 'pending' | 'unavailable';
  eta?: string;
}

export interface Evidence {
  type: 'photo' | 'video';
  url: string;
  description?: string;
  uploadedAt: string;
}

export interface Diagnosis {
  _id: string;
  orderId: string;
  diagnosisNotes: string;
  evidence: Evidence[];
  completedAt: string;
  completedBy: string;
}

export interface Quotation {
  _id: string;
  orderId: string;
  quotationNumber: string;
  status: 'draft' | 'pending-approval' | 'approved' | 'rejected' | 'partially-approved';
  services: QuotationService[];
  totalCost: number;
  validUntil: string;
  createdAt: string;
  updatedAt: string;
}

export interface QuotationService {
  serviceId: string;
  serviceName: string;
  description: string;
  estimatedCost: number;
  laborCost: number;
  partsCost: number;
  estimatedDuration: number;
  isApproved?: boolean;
  rejectionReason?: string;
}

// API Request/Response interfaces
export interface CreateOrderRequest {
  stockId: string;
  associateId: string;
  workshopId: string;
  type: 'customer-initiated' | 'preventive-detected';
  failureType: 'known' | 'unknown';
  arrivalMethod: 'driving' | 'tow-truck';
  customerDescription: string;
  canVehicleDrive: boolean;
  needsTowTruck: boolean;
  approvalType: 'fleet' | 'customer';
}

export interface CompleteDiagnosisRequest {
  diagnosisNotes: string;
  services: {
    serviceType: string;
    serviceName: string;
    description: string;
    estimatedCost: number;
    laborCost: number;
    estimatedDuration: number;
    parts: Omit<ServicePart, 'totalCost'>[];
  }[];
  evidence?: File[];
}

export interface CreateQuotationRequest {
  services: {
    serviceId: string;
    estimatedCost: number;
    laborCost: number;
    estimatedDuration: number;
  }[];
  validUntil: string;
}

export interface ProcessApprovalRequest {
  decisions: {
    serviceId: string;
    isApproved: boolean;
    rejectionReason?: string;
  }[];
}

export interface UpdateServiceProgressRequest {
  status: 'not-started' | 'in-progress' | 'completed' | 'waiting-for-parts';
  notes?: string;
  evidence?: File[];
}

// API Response interface
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Service methods
export const correctiveMaintenanceService = {
  setHeaders: (headers: Record<string, string>) => {
    apiCorrectiveMaintenance.defaults.headers = {
      ...apiCorrectiveMaintenance.defaults.headers,
      ...headers,
    };
  },

  // Orders
  createOrder: async (orderData: CreateOrderRequest): Promise<ApiResponse<CorrectiveMaintenanceOrder>> => {
    const { data } = await apiCorrectiveMaintenance.post('/orders', orderData);
    return data;
  },

  getOrders: async (params?: {
    page?: number;
    limit?: number;
    status?: string;
    workshopId?: string;
  }): Promise<ApiResponse<CorrectiveMaintenanceOrder[]>> => {
    const { data } = await apiCorrectiveMaintenance.get('/orders', { params });
    return data;
  },

  getOrderById: async (orderId: string): Promise<ApiResponse<CorrectiveMaintenanceOrder>> => {
    const { data } = await apiCorrectiveMaintenance.get(`/orders/${orderId}`);
    return data;
  },

  // Diagnosis
  completeDiagnosis: async (orderId: string, diagnosisData: CompleteDiagnosisRequest): Promise<ApiResponse<Diagnosis>> => {
    const formData = new FormData();
    formData.append('diagnosisNotes', diagnosisData.diagnosisNotes);
    formData.append('services', JSON.stringify(diagnosisData.services));
    
    if (diagnosisData.evidence) {
      diagnosisData.evidence.forEach((file, index) => {
        formData.append(`evidence`, file);
      });
    }

    const { data } = await apiCorrectiveMaintenance.post(`/orders/${orderId}/diagnosis`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
    return data;
  },

  // Quotations
  createQuotation: async (orderId: string, quotationData: CreateQuotationRequest): Promise<ApiResponse<Quotation>> => {
    const { data } = await apiCorrectiveMaintenance.post(`/orders/${orderId}/quotation`, quotationData);
    return data;
  },

  getQuotations: async (params?: {
    page?: number;
    limit?: number;
    status?: string;
  }): Promise<ApiResponse<Quotation[]>> => {
    const { data } = await apiCorrectiveMaintenance.get('/quotations', { params });
    return data;
  },

  submitQuotation: async (quotationId: string): Promise<ApiResponse<Quotation>> => {
    const { data } = await apiCorrectiveMaintenance.post(`/quotations/${quotationId}/submit`);
    return data;
  },

  // Approvals
  processApproval: async (quotationId: string, approvalData: ProcessApprovalRequest): Promise<ApiResponse<Quotation>> => {
    const { data } = await apiCorrectiveMaintenance.post(`/quotations/${quotationId}/approve`, approvalData);
    return data;
  },

  // Service Execution
  startWork: async (orderId: string): Promise<ApiResponse<CorrectiveMaintenanceOrder>> => {
    const { data } = await apiCorrectiveMaintenance.post(`/orders/${orderId}/start`);
    return data;
  },

  updateServiceProgress: async (serviceId: string, progressData: UpdateServiceProgressRequest): Promise<ApiResponse<CorrectiveService>> => {
    const formData = new FormData();
    formData.append('status', progressData.status);
    if (progressData.notes) formData.append('notes', progressData.notes);
    
    if (progressData.evidence) {
      progressData.evidence.forEach((file) => {
        formData.append('evidence', file);
      });
    }

    const { data } = await apiCorrectiveMaintenance.patch(`/services/${serviceId}/progress`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
    return data;
  },

  completeOrder: async (orderId: string): Promise<ApiResponse<CorrectiveMaintenanceOrder>> => {
    const { data } = await apiCorrectiveMaintenance.post(`/orders/${orderId}/complete`);
    return data;
  },
};
