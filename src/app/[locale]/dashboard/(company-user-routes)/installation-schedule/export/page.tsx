'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import * as XLSX from 'xlsx';
import { format } from 'date-fns';
import { companyService } from '@/constants/companyService';
import { DateRangePicker } from '@/components/ui/date-range-picker';
import { DateRange } from "react-day-picker"

import { Datum, Status } from './types';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@chakra-ui/react';

// const statusOptions: any[] = ['scheduled', 'installed', 'completed'];

const statusOptions: any[] = [
  {
    label: 'Agendada',
    value: 'scheduled',
  },
  {
    label: 'Instalada',
    value: 'installed',
  },
  {
    label: 'Completada',
    value: 'completed',
  },
  {
    label: 'No Atendida',
    value: 'not-attended',
  },
]

export default function ExportInstallations() {
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);
  const [data, setData] = useState<Datum[]>([]);
  const [loading, setLoading] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [selectedStatuses, setSelectedStatuses] = useState<Status[]>([]);
  const toast = useToast();


  const handleStatusChange = (status: Status, checked: boolean) => {
    setSelectedStatuses((prev) =>
      checked ? [...prev, status] : prev.filter((s) => s !== status)
    );
  };

  const handleExport = () => {
    setExporting(true);
    try {
      const worksheet = XLSX.utils.json_to_sheet(
        data.map((item) => ({
          ID: item._id,
          Calle: item.address.street,
          Colonia: item.address.colony,
          'Código Postal': item.address.zipCode,
          'Número Exterior': item.address.exteriorNumber,
          'Número Interior': item.address.interiorNumber,
          Referencias: item.address.references,
          'Nombre del Asociado': `${item.associate.firstName} ${item.associate.lastName}`,
          'Correo del Asociado': item.associate.email,
          'Teléfono del Asociado': item.associate.phone,
          Ciudad: item.city.name,
          Estado: item.city.state,
          'Fecha y Hora de Inicio': format(new Date(item.startTime), 'dd/MM/yyyy HH:mm'),
          'Fecha y Hora de Fin': format(new Date(item.endTime), 'dd/MM/yyyy HH:mm'),
          'Estado de la Cita': item.status,
          'Nombre del Equipo': item.crew.name,
          'Nombre de la Colonia': item.neighborhood.name,
          'Marca del Vehículo': item.stockVehicle.brand,
          'Modelo del Vehículo': item.stockVehicle.model,
          'Número de Contrato': item.stockVehicle.contractNumber,
          'Placas del Vehículo': item.stockVehicle.carPlates.plates,
          VIN: item.stockVehicle.vin,
          // 'Número de Reprogramaciones': item.rescheduleCount,
          'Fecha de Creación': format(new Date(item.createdAt), 'dd/MM/yyyy HH:mm'),
          // 'Fecha de Actualización': format(new Date(item.updatedAt), 'dd/MM/yyyy HH:mm'),
        }))
      );
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Instalaciones');
      // XLSX.writeFile(workbook, 'instalaciones.xlsx');

      // name the file based on filters:
      const fileName = `instalaciones-${dateRange?.from?.toLocaleDateString('es-ES', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
      })}-${dateRange?.to?.toLocaleDateString('es-ES', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
      })}-${selectedStatuses.join('-')}.xlsx`;

      XLSX.writeFile(workbook, fileName);

    } catch (error) {
      console.error('Error al exportar los datos:', error);
      toast({
        title: 'Error al exportar los datos',
        status: 'error',
        duration: 3000,
        isClosable: true,
        position: 'top',
      });
    } finally {
      setExporting(false);
    }
  };

  const handleApplyFilters = async () => {
    if (!dateRange?.from || !dateRange?.to) {
      toast({
        title: 'Selecciona un rango de fechas válido.',
        status: 'error',
        duration: 3000,
        isClosable: true,
        position: 'top',
      });
      return;
    }

    setLoading(true);
    try {
      console.log('status to filter: ', selectedStatuses);
      const response = await companyService.getInstallationsFiltered(
        dateRange.from.toISOString(),
        dateRange.to.toISOString(),
        undefined,
        undefined,
        // selectedStatuses.length ? selectedStatuses : undefined
        // make it string
        selectedStatuses.length ? selectedStatuses.join(',') : undefined
      );
      setData(response.data);
    } catch (error) {
      toast({
        title: 'Error al obtener los datos',
        status: 'error',
        duration: 3000,
        isClosable: true,
        position: 'top',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-4 space-y-4">
      <DateRangePicker date={dateRange} setDate={setDateRange} />

      {/* <div className="flex flex-wrap gap-4">
        {statusOptions.map((status) => (
          <div key={status} className="flex items-center space-x-2">
            <Checkbox
              id={status}
              checked={selectedStatuses.includes(status)}
              onCheckedChange={(checked) =>
                handleStatusChange(status, checked as boolean)
              }
            />
            <Label htmlFor={status} className="capitalize">
              {status}
            </Label>
          </div>
        ))}
      </div> */}

      <div className="space-y-4">
        <p className="text-sm text-gray-600">
          Selecciona los estatus que deseas exportar:
        </p>
        <div className='flex flex-wrap gap-4 '>

          {statusOptions.map((status) => (
            <div key={status.value} className="flex items-center space-x-2">
              <Checkbox
                id={status.value}
                checked={selectedStatuses.includes(status.value)}
                onCheckedChange={(checked) =>
                  handleStatusChange(status.value, checked as boolean)
                }
              />
              <Label htmlFor={status.value} className="capitalize">
                {status.label}
              </Label>
            </div>
          ))}
        </div>

      </div>

      <div className="flex space-x-4">
        <Button onClick={handleApplyFilters} disabled={loading}>
          {loading ? 'Cargando...' : 'Aplicar Filtros'}
        </Button>
        <Button onClick={handleExport} disabled={data.length === 0 || exporting}>
          {exporting ? 'Generando archivo...' : 'Exportar Información'}
        </Button>
      </div>
      {data.length > 0 && (
        <p className="text-sm text-gray-600">
          Total de resultados: <strong>{data.length}</strong>
        </p>
      )}
    </div>
  );
}
