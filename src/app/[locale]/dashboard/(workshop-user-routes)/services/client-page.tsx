'use client';

import { IService } from '../vehicles/[stockId]/_actions/getServicesByVehicleId';
import { Card, CardContent } from '@/components/ui/card';
import { format } from 'date-fns';
import { es } from 'date-fns/esm/locale';
import { statusMap, typeServiceMap } from '@/constants';
import { Menu, MenuButton, MenuItem, MenuList, useToast } from '@chakra-ui/react';
import ServiceDetail from '../vehicles/[stockId]/_components/service-detail';
import { Ellipsis } from 'lucide-react';
import { ContinueServiceModal } from '../vehicles/[stockId]/_components/continue-service-modal';

interface ServicesClientPageProps {
  services: IService[];
}

export default function ServicesClientPage({ services }: ServicesClientPageProps) {

  // console.log('services', services)

  const toast = useToast();

  return (

    <div className='flex flex-col gap-4 pb-10'>
      <h1 className='font-bold text-xl'>Todos los Servicios</h1>
      <Card >
        <CardContent className="p-6 ">

          <div className="overflow-x-auto min-h-[500px]">
            <table className="w-full">
              <thead>
                <tr className="text-left bg-gray-50">
                  <th className="p-3 !max-w-[150px]">
                    ID
                  </th>
                  <th className="p-3 ">Placa</th>
                  <th className="p-3 ">No. de contacto</th>
                  <th className="p-3 ">Taller</th>
                  <th className="p-3 ">Tipo de servicio</th>
                  <th className="p-3 min-w-[250px]">Fecha</th>
                  <th className="p-3 min-w-[150px] ">Costo</th>
                  <th className="p-3">Estado</th>
                  <th className="p-3">Ver</th>
                  <th className="p-3">Acciones</th>
                </tr>
              </thead>
              <tbody>
                {services.map((service, index) => (
                  <tr key={index} className="border-b hover:bg-gray-100 transition-colors ">
                    <td className="p-3 cursor-pointer" onClick={() => {
                      navigator.clipboard.writeText(service._id);
                      toast({
                        title: 'ID copiado al portapapeles',
                        status: 'success',
                        position: 'top',
                        duration: 3000,
                        isClosable: true,
                      })
                    }}>
                      {service._id.substring(0, 6)}
                    </td>

                    <td className="p-3">
                      {
                        service.stock?.carPlates?.plates
                      }
                    </td>
                    <td className="p-3">
                      {
                        service.stock?.associates[service.stock?.associates.length - 1]?.phone
                      }
                    </td>
                    <td className="p-3">
                      {service.workshop?.name}
                    </td>

                    <td className="p-3">
                      {/* {service.secondStep?.serviceDetail.serviceType || 'Pendiente'} */}
                      {
                        typeServiceMap[service.secondStep?.serviceDetail.serviceType || 'Pendiente por definir']
                      }
                    </td>
                    <td>
                      {/* Format like: May 1, 2024 */}
                      {
                        formatDate(service.createdAt)
                      }

                    </td>

                    <td className="p-3">
                      {!service.secondStep?.costsAndTimes?.totalCost || service.status === 'pending' && 'En proceso'}
                      {
                        service.status === 'completed' && service.secondStep?.costsAndTimes?.totalCost || 'Sin especificar'
                      }

                    </td>

                    <td className="p-3">
                      <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm">
                        {/* {service.status || 'Pendiente'} */}
                        {statusMap[service.status] || 'Pendiente'}
                      </span>
                    </td>
                    <td className="p-3">


                      <ServiceDetail
                        service={service}
                      />
                    </td>
                    <td>
                      <Menu>
                        {({ isOpen }) => (
                          <>
                            <MenuButton className='bg-primaryBtn text-white py-2 px-3 rounded-md' >

                              <Ellipsis />

                            </MenuButton>
                            <MenuList >
                              {
                                service.status === 'pending' && (
                                  <MenuItem>
                                    <ContinueServiceModal serviceId={service._id} />
                                  </MenuItem>
                                )
                              }

                              <MenuItem >Editar</MenuItem>

                            </MenuList>
                          </>
                        )}
                      </Menu>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

function formatDate(date: string) {
  const parsedDate = format(new Date(date), 'MMMM dd, yyyy HH:mm', {
    locale: es
  })

  return parsedDate[0].toUpperCase() + parsedDate.slice(1)
}