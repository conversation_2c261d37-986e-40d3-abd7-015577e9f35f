'use client';
import React, { use<PERSON>emo } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent } from '@/components/ui/card';
import { ChevronLeft, Ellipsis, FileText } from 'lucide-react';
import VehicleSVGV2 from '@/svgsComponents/VehicleSVG_V2';
import { colors, svgColors } from '@/constants';
import { RegisterServiceModal } from './_components/RegisterServiceModal';
import { VehicleDetail } from '../_actions/getVehicleDetailById';
import { getServicesByVehicleId, IService } from './_actions/getServicesByVehicleId';
import { format } from 'date-fns';
import { es } from 'date-fns/esm/locale'
import ServiceDetail, { typeServiceMap } from './_components/service-detail';
import { Button } from '@/components/ui/button';
import { <PERSON>u, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>uI<PERSON>, <PERSON>uList, useToast } from '@chakra-ui/react';
import { ContinueServiceModal } from './_components/continue-service-modal';
import { useRouter } from 'next/navigation';


interface VehicleDetailProps {
  params: {
    stockId: string;
  };
  vehicle: VehicleDetail;
}

export default function VehicleDetailClientPage({ params, vehicle }: VehicleDetailProps) {
  const [tab, setTab] = React.useState('vehicle-data');

  const [services, setServices] = React.useState<IService[]>([])
  const router = useRouter()

  async function fetch() {

    const result = await getServicesByVehicleId(vehicle._id)

    if (result) {
      setServices(result)
    }

  }

  React.useEffect(() => {

    fetch()

  }, [])

  const existsPendingService = useMemo(() => {
    return services.some(service => service.status === 'pending')
  }, [services])

  function onChangeTab(tab: string) {
    setTab(tab);
  }


  return (
    <div className=" mx-auto  space-y-6 ">
      {/* Header */}
      <div className='flex justify-between items-center flex-wrap'>

        <div className="flex items-center space-x-2 text-lg  font-bold">
          <ChevronLeft className="w-5 h-5 cursor-pointer"
            onClick={() => {
              console.log('document.referrer', document.referrer, document.referrer.includes(window.location.origin))

              if (document.referrer && document.referrer.includes(window.location.origin)) {
                const origin = document.referrer
                router.push(origin)
              } else {
                router.push('/dashboard/vehicles')
              }

            }}
          />
          <span className='' >{vehicle.brand} {vehicle.model} | {vehicle.contractNumber || vehicle.carNumber}  </span>
        </div>

        <div className='flex items-center space-x-4 '>

          {
            existsPendingService && (

              <Button
                className="flex items-center space-x-2 text-lg bg-primaryBtn hover:bg-purple-800 rounded-md w-full"
                onClick={() => setTab('service-history')}
              >
                <FileText className="w-5 h-5 text-white" />
                <span className='text-white'>Existe un servicio pendiente</span>
              </Button>

            )
          }

          {
            !existsPendingService && (
              <RegisterServiceModal vehicle={vehicle} />
            )
          }
        </div>
      </div>

      <Tabs defaultValue={tab} className="w-[full] h-full" value={tab} /* onChange={onChangeTab} */ onValueChange={onChangeTab}>
        <TabsList className="mb-4 h-full w-[max-content] flex justify-start flex-col lg:flex-row items-start gap-2 lg:gap-0 ">
          {/* I want to add this className when tab is active: bg-primaryBtn text-white, the below example doesn't work  */}
          <TabsTrigger value="vehicle-data" className="px-4 data-[state=active]:bg-primaryBtn p-3 data-[state=active]:text-white ">
            Información del Vehículo
          </TabsTrigger>
          <TabsTrigger value="service-history" className="px-4 data-[state=active]:bg-primaryBtn p-3 data-[state=active]:text-white relative">
            {/* Service History */}
            Historial de Servicios
            {
              existsPendingService && (
                <span className="absolute -top-1 -right-1 bg-red-500 w-3 h-3 text-white rounded-full  text-xs">
                </span>
              )
            }
          </TabsTrigger>
        </TabsList>

        <TabsContent value="vehicle-data">
          <Card>
            {/* Add animation to all */}
            <CardContent className="p-6" >
              <div className="grid grid-cols-1 md:grid-cols-5 gap-8  border-b border-b-gray-300">
                {/* Vehicle Image */}
                <div className="w-full col-span-1 md:col-span-2">
                  <VehicleSVGV2 color={svgColors[vehicle.color.toUpperCase()] || 'white'} />
                </div>

                {/* Vehicle Details */}
                <div className="space-y-4 col-span-1 md:col-span-3">
                  <div className="flex flex-col ">
                    <h2 className="text-2xl font-semibold">{vehicle.brand} {vehicle.model}</h2>
                  </div>

                  <div className="space-y-2">
                    {[
                      // { label: 'Brand', value: `${vehicle.brand}` },
                      { label: 'Color', value: colors.find((color) => color.value === vehicle.color)?.label?.toUpperCase() || 'BLANCO' },
                      { label: 'Placas', value: vehicle.carPlates?.plates },
                      { label: 'VIN', value: vehicle.vin },
                      { label: 'KM', value: vehicle.km },
                    ].map((item) => (
                      <div key={item.label} className="flex gap-3 "  >
                        {/* border-b pb-2 */}
                        <span className="text-gray-600">{item.label}</span>
                        <span className="font-medium">{item.value}</span>
                      </div>
                    ))}
                  </div>




                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <ServiceHistoryTab services={services} />
      </Tabs>
    </div>
  );
};

function formatDate(date: string) {
  const parsedDate = format(new Date(date), 'MMMM dd, yyyy HH:mm', {
    locale: es
  })

  return parsedDate[0].toUpperCase() + parsedDate.slice(1)
}

const statusMap: Record<string, string> = {
  pending: 'Pendiente',
  completed: 'Completado',
}

function ServiceHistoryTab({ services }: { services: IService[]; }) {

  const toast = useToast();

  return (

    <TabsContent value="service-history" >
      <Card >
        <CardContent className="p-6 ">
          <div className="overflow-x-auto min-h-[500px]">
            <table className="w-full">
              <thead>
                <tr className="text-left bg-gray-50">
                  <th className="p-3 !max-w-[300px]">
                    ID
                  </th>
                  <th className="p-3">
                    Taller
                  </th>
                  <th className="p-3 ">Tipo de servicio</th>
                  <th className="p-3 min-w-[300px]">Fecha</th>
                  <th className="p-3">Costo</th>
                  <th className="p-3">Estado</th>
                  <th className="p-3">Ver</th>
                  <th className="p-3">Acciones</th>
                </tr>
              </thead>
              <tbody>
                {services.map((service, index) => (
                  <tr key={index} className="border-b">
                    <td className="p-3 cursor-pointer" onClick={() => {
                      navigator.clipboard.writeText(service._id);
                      toast({
                        title: 'ID copiado al portapapeles',
                        status: 'success',
                        position: 'top',
                        duration: 3000,
                        isClosable: true,
                      })
                    }}>
                      {service._id.substring(0, 6)}
                    </td>
                    <td className="p-3">
                      {service.workshop?.name}
                    </td>

                    <td className="p-3">
                      {/* {service.secondStep?.serviceDetail.serviceType || 'Pendiente'} */}
                      {
                        typeServiceMap[service.secondStep?.serviceDetail.serviceType || 'Pendiente por definir']
                      }
                    </td>
                    <td>
                      {/* Format like: May 1, 2024 */}
                      {
                        formatDate(service.createdAt)
                      }

                    </td>

                    <td className="p-3">{service.secondStep?.costsAndTimes?.totalCost || 'En proceso'}</td>

                    <td className="p-3">
                      <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm">
                        {/* {service.status || 'Pendiente'} */}
                        {statusMap[service.status] || 'Pendiente'}
                      </span>
                    </td>
                    <td className="p-3">


                      <ServiceDetail
                        service={service}
                      />
                    </td>
                    <td>
                      <Menu>
                        {({ isOpen }) => (
                          <>
                            <MenuButton className='bg-primaryBtn text-white py-2 px-3 rounded-md' >

                              <Ellipsis />

                            </MenuButton>
                            <MenuList >
                              {
                                service.status === 'pending' && (
                                  <MenuItem>
                                    <ContinueServiceModal serviceId={service._id} />
                                  </MenuItem>
                                )
                              }

                              <MenuItem >Editar</MenuItem>

                            </MenuList>
                          </>
                        )}
                      </Menu>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </TabsContent>

  )
}
