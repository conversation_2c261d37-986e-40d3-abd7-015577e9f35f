import { getVehicleDetailById } from '../_actions/getVehicleDetailById';
import VehicleDetailClientPage from './client';


export default async function VehicleDetailPage({ params }: { params: Promise<{ stockId: string }> }) {

  const awaitedParams = await params;

  const vehicle = await getVehicleDetailById(awaitedParams.stockId);

  if (!vehicle) {
    return <div>Vehicle not found</div>
  }

  return (
    <>

      <VehicleDetailClientPage params={awaitedParams} vehicle={vehicle} />
    </>
  )
}
