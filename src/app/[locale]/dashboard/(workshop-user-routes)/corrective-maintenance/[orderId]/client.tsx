'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ArrowLeft, FileText, Wrench, CheckCircle, Clock, AlertTriangle } from 'lucide-react';
import { 
  CorrectiveMaintenanceOrder,
  getStatusLabel,
  getStatusColor,
  formatCurrency,
  formatDuration,
  ORDER_TYPE_LABELS,
  FAILURE_TYPE_LABELS,
  ARRIVAL_METHOD_LABELS,
  APPROVAL_TYPE_LABELS,
  isOrderEditable,
  canStartWork,
  canCompleteOrder
} from '../types';
import DiagnosisModal from '../_components/DiagnosisModal';
import QuotationModal from '../_components/QuotationModal';
import ApprovalModal from '../_components/ApprovalModal';
import ServiceProgressModal from '../_components/ServiceProgressModal';

interface OrderDetailClientProps {
  order: CorrectiveMaintenanceOrder;
}

export default function OrderDetailClient({ order }: OrderDetailClientProps) {
  const router = useRouter();
  const [isDiagnosisModalOpen, setIsDiagnosisModalOpen] = useState(false);
  const [isQuotationModalOpen, setIsQuotationModalOpen] = useState(false);
  const [isApprovalModalOpen, setIsApprovalModalOpen] = useState(false);
  const [isServiceProgressModalOpen, setIsServiceProgressModalOpen] = useState(false);
  const [selectedServiceId, setSelectedServiceId] = useState<string | null>(null);

  const handleBack = () => {
    router.push('/dashboard/corrective-maintenance');
  };

  const handleDiagnosisComplete = () => {
    setIsDiagnosisModalOpen(false);
    router.refresh();
  };

  const handleQuotationCreated = () => {
    setIsQuotationModalOpen(false);
    router.refresh();
  };

  const handleApprovalProcessed = () => {
    setIsApprovalModalOpen(false);
    router.refresh();
  };

  const handleServiceProgressUpdated = () => {
    setIsServiceProgressModalOpen(false);
    setSelectedServiceId(null);
    router.refresh();
  };

  const handleUpdateServiceProgress = (serviceId: string) => {
    setSelectedServiceId(serviceId);
    setIsServiceProgressModalOpen(true);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Volver
          </Button>
          <div>
            <h1 className="text-2xl font-bold">
              Orden #{order._id.slice(-8).toUpperCase()}
            </h1>
            <p className="text-gray-600">
              {ORDER_TYPE_LABELS[order.type]} • {FAILURE_TYPE_LABELS[order.failureType]}
            </p>
          </div>
        </div>
        <Badge className={getStatusColor(order.status, 'order')} variant="secondary">
          {getStatusLabel(order.status, 'order')}
        </Badge>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-2">
        {order.status === 'pending' && (
          <Button onClick={() => setIsDiagnosisModalOpen(true)}>
            <FileText className="h-4 w-4 mr-2" />
            Completar Diagnóstico
          </Button>
        )}
        
        {order.status === 'diagnosed' && (
          <Button onClick={() => setIsQuotationModalOpen(true)}>
            <FileText className="h-4 w-4 mr-2" />
            Crear Cotización
          </Button>
        )}
        
        {order.status === 'quoted' && (
          <Button onClick={() => setIsApprovalModalOpen(true)}>
            <CheckCircle className="h-4 w-4 mr-2" />
            Procesar Aprobación
          </Button>
        )}
        
        {canStartWork(order.status) && (
          <Button>
            <Wrench className="h-4 w-4 mr-2" />
            Iniciar Trabajo
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Order Information */}
          <Card>
            <CardHeader>
              <CardTitle>Información de la Orden</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">Vehículo</p>
                  <p className="text-sm">
                    {order.vehicle?.brand} {order.vehicle?.model}
                  </p>
                  <p className="text-xs text-gray-500">
                    {order.vehicle?.carNumber} • {order.vehicle?.carPlates?.plates}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Taller</p>
                  <p className="text-sm">{order.workshop?.name}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Método de Llegada</p>
                  <p className="text-sm">{ARRIVAL_METHOD_LABELS[order.arrivalMethod]}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Tipo de Aprobación</p>
                  <p className="text-sm">{APPROVAL_TYPE_LABELS[order.approvalType]}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Fecha de Creación</p>
                  <p className="text-sm">
                    {format(new Date(order.createdAt), 'dd/MM/yyyy HH:mm', { locale: es })}
                  </p>
                </div>
                {order.totalEstimatedCost && (
                  <div>
                    <p className="text-sm font-medium text-gray-500">Costo Total Estimado</p>
                    <p className="text-sm font-semibold">
                      {formatCurrency(order.totalEstimatedCost)}
                    </p>
                  </div>
                )}
              </div>
              
              <Separator />
              
              <div>
                <p className="text-sm font-medium text-gray-500 mb-2">Descripción del Problema</p>
                <p className="text-sm bg-gray-50 p-3 rounded-md">
                  {order.customerDescription}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Diagnosis */}
          {order.diagnosis && (
            <Card>
              <CardHeader>
                <CardTitle>Diagnóstico</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm bg-gray-50 p-3 rounded-md">
                  {order.diagnosis.diagnosisNotes}
                </p>
                <p className="text-xs text-gray-500 mt-2">
                  Completado el {format(new Date(order.diagnosis.completedAt), 'dd/MM/yyyy HH:mm', { locale: es })}
                </p>
              </CardContent>
            </Card>
          )}

          {/* Services */}
          {order.services && order.services.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Servicios</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {order.services.map((service) => (
                    <div key={service._id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{service.serviceName}</h4>
                        <div className="flex items-center space-x-2">
                          <Badge 
                            className={getStatusColor(service.status, 'service')}
                            variant="secondary"
                          >
                            {getStatusLabel(service.status, 'service')}
                          </Badge>
                          {order.status === 'in-progress' && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleUpdateServiceProgress(service._id)}
                            >
                              Actualizar
                            </Button>
                          )}
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{service.description}</p>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                        <div>
                          <span className="font-medium">Costo: </span>
                          {formatCurrency(service.estimatedCost)}
                        </div>
                        <div>
                          <span className="font-medium">Mano de obra: </span>
                          {formatCurrency(service.laborCost)}
                        </div>
                        <div>
                          <span className="font-medium">Duración: </span>
                          {formatDuration(service.estimatedDuration)}
                        </div>
                        <div>
                          <span className="font-medium">Refacciones: </span>
                          {service.parts.length}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Status Timeline */}
          <Card>
            <CardHeader>
              <CardTitle>Estado de la Orden</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className={`flex items-center space-x-2 ${
                  ['pending', 'diagnosed', 'quoted', 'approved', 'in-progress', 'completed'].includes(order.status) 
                    ? 'text-green-600' : 'text-gray-400'
                }`}>
                  <CheckCircle className="h-4 w-4" />
                  <span className="text-sm">Orden Creada</span>
                </div>
                <div className={`flex items-center space-x-2 ${
                  ['diagnosed', 'quoted', 'approved', 'in-progress', 'completed'].includes(order.status) 
                    ? 'text-green-600' : 'text-gray-400'
                }`}>
                  <CheckCircle className="h-4 w-4" />
                  <span className="text-sm">Diagnóstico Completado</span>
                </div>
                <div className={`flex items-center space-x-2 ${
                  ['quoted', 'approved', 'in-progress', 'completed'].includes(order.status) 
                    ? 'text-green-600' : 'text-gray-400'
                }`}>
                  <CheckCircle className="h-4 w-4" />
                  <span className="text-sm">Cotización Creada</span>
                </div>
                <div className={`flex items-center space-x-2 ${
                  ['approved', 'in-progress', 'completed'].includes(order.status) 
                    ? 'text-green-600' : 'text-gray-400'
                }`}>
                  <CheckCircle className="h-4 w-4" />
                  <span className="text-sm">Servicios Aprobados</span>
                </div>
                <div className={`flex items-center space-x-2 ${
                  ['in-progress', 'completed'].includes(order.status) 
                    ? 'text-green-600' : 'text-gray-400'
                }`}>
                  <Clock className="h-4 w-4" />
                  <span className="text-sm">Trabajo en Progreso</span>
                </div>
                <div className={`flex items-center space-x-2 ${
                  order.status === 'completed' ? 'text-green-600' : 'text-gray-400'
                }`}>
                  <CheckCircle className="h-4 w-4" />
                  <span className="text-sm">Orden Completada</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Stats */}
          {order.services && order.services.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Resumen</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Total de Servicios:</span>
                  <span className="text-sm font-medium">{order.services.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Completados:</span>
                  <span className="text-sm font-medium">
                    {order.services.filter(s => s.status === 'completed').length}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">En Progreso:</span>
                  <span className="text-sm font-medium">
                    {order.services.filter(s => s.status === 'in-progress').length}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Esperando Refacciones:</span>
                  <span className="text-sm font-medium">
                    {order.services.filter(s => s.status === 'waiting-for-parts').length}
                  </span>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Modals */}
      <DiagnosisModal
        isOpen={isDiagnosisModalOpen}
        onClose={() => setIsDiagnosisModalOpen(false)}
        onSuccess={handleDiagnosisComplete}
        orderId={order._id}
      />

      <QuotationModal
        isOpen={isQuotationModalOpen}
        onClose={() => setIsQuotationModalOpen(false)}
        onSuccess={handleQuotationCreated}
        order={order}
      />

      <ApprovalModal
        isOpen={isApprovalModalOpen}
        onClose={() => setIsApprovalModalOpen(false)}
        onSuccess={handleApprovalProcessed}
        quotation={order.quotation}
      />

      {selectedServiceId && (
        <ServiceProgressModal
          isOpen={isServiceProgressModalOpen}
          onClose={() => setIsServiceProgressModalOpen(false)}
          onSuccess={handleServiceProgressUpdated}
          serviceId={selectedServiceId}
          service={order.services?.find(s => s._id === selectedServiceId)}
        />
      )}
    </div>
  );
}
