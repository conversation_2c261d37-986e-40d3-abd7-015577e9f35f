'use server';

import { URL_API } from '@/constants';
import { cache } from 'react';
import axios from 'axios';
import getCurrentUser from '@/actions/getCurrentUser';
import { ApiResponse } from '../types';

interface Vehicle {
  _id: string;
  brand: string;
  model: string;
  carNumber: string;
  carPlates: {
    plates: string;
  };
  status: string;
}

export const getVehicles = cache(async (): Promise<ApiResponse<Vehicle[]> | null> => {
  const user = await getCurrentUser();
  if (!user) return null;

  try {
    const response = await axios.get(
      `${URL_API}/vendor-platform/vehicles`,
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    return {
      success: true,
      data: response.data.data || response.data,
      message: 'Vehículos obtenidos exitosamente',
    };
  } catch (error: any) {
    console.error('Error getting vehicles:', error.response?.data || error);
    return {
      success: false,
      data: [],
      message: 'Error al obtener los vehículos',
    };
  }
});
