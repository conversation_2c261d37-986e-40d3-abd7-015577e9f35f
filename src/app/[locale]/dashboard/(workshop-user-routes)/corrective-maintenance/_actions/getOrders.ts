'use server';

import { URL_API } from '@/constants';
import { cache } from 'react';
import axios from 'axios';
import getCurrentUser from '@/actions/getCurrentUser';
import { CorrectiveMaintenanceOrder, ApiResponse } from '../types';

interface GetOrdersParams {
  page?: number;
  limit?: number;
  status?: string;
  workshopId?: string;
  search?: string;
}

export const getOrders = cache(async (params: GetOrdersParams = {}) => {
  const user = await getCurrentUser();
  if (!user) return null;

  try {
    const url = new URL(`${URL_API}/vendor-platform/corrective-maintenance/orders`);
    
    // Add query parameters
    if (params.page) url.searchParams.append('page', params.page.toString());
    if (params.limit) url.searchParams.append('limit', params.limit.toString());
    if (params.status) url.searchParams.append('status', params.status);
    if (params.workshopId) url.searchParams.append('workshopId', params.workshopId);
    if (params.search) url.searchParams.append('search', params.search);

    const response = await axios.get(url.toString(), {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    return response.data as ApiResponse<CorrectiveMaintenanceOrder[]>;
  } catch (error: any) {
    console.error('Error getting corrective maintenance orders:', error.response?.data || error);
    return {
      success: false,
      data: [],
      message: 'Error al obtener las órdenes de mantenimiento correctivo',
      pagination: {
        page: params.page || 1,
        limit: params.limit || 10,
        total: 0,
        totalPages: 0,
      },
    } as ApiResponse<CorrectiveMaintenanceOrder[]>;
  }
});

export const getOrderById = cache(async (orderId: string) => {
  const user = await getCurrentUser();
  if (!user) return null;

  try {
    const response = await axios.get(
      `${URL_API}/vendor-platform/corrective-maintenance/orders/${orderId}`,
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    return response.data as ApiResponse<CorrectiveMaintenanceOrder>;
  } catch (error: any) {
    console.error('Error getting corrective maintenance order:', error.response?.data || error);
    return {
      success: false,
      data: null,
      message: 'Error al obtener la orden de mantenimiento correctivo',
    } as ApiResponse<CorrectiveMaintenanceOrder>;
  }
});
