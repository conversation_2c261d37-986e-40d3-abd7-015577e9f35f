'use server';

import { URL_API } from '@/constants';
import axios from 'axios';
import getCurrentUser from '@/actions/getCurrentUser';
import { CompleteDiagnosisRequest, ApiResponse, Diagnosis } from '../types';

export async function completeDiagnosis(
  orderId: string, 
  diagnosisData: CompleteDiagnosisRequest
): Promise<ApiResponse<Diagnosis>> {
  const user = await getCurrentUser();
  
  if (!user) {
    return {
      success: false,
      data: null as any,
      message: 'Usuario no autenticado',
    };
  }

  try {
    const response = await axios.post(
      `${URL_API}/vendor-platform/corrective-maintenance/orders/${orderId}/diagnosis`,
      diagnosisData,
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    return {
      success: true,
      data: response.data.data,
      message: 'Diagnóstico completado exitosamente',
    };
  } catch (error: any) {
    console.error('Error completing diagnosis:', error.response?.data || error);
    
    return {
      success: false,
      data: null as any,
      message: error.response?.data?.message || 'Error al completar el diagnóstico',
    };
  }
}
