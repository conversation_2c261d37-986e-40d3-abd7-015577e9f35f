'use server';

import { URL_API } from '@/constants';
import { cache } from 'react';
import axios from 'axios';
import getCurrentUser from '@/actions/getCurrentUser';
import { ApiResponse } from '../types';

interface Workshop {
  _id: string;
  name: string;
  address?: string;
  phone?: string;
  email?: string;
}

export const getWorkshops = cache(async (): Promise<ApiResponse<Workshop[]> | null> => {
  const user = await getCurrentUser();
  if (!user) return null;

  try {
    // Try to get workshops from the organization
    const response = await axios.get(
      `${URL_API}/vendor-platform/organizations/${user.organizationId}/workshops`,
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    return {
      success: true,
      data: response.data.data || response.data,
      message: 'Talleres obtenidos exitosamente',
    };
  } catch (error: any) {
    console.error('Error getting workshops:', error.response?.data || error);
    
    // Fallback: try to get workshops from a different endpoint
    try {
      const fallbackResponse = await axios.get(
        `${URL_API}/vendor-platform/workshops`,
        {
          headers: {
            Authorization: `Bearer ${user.accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      );

      return {
        success: true,
        data: fallbackResponse.data.data || fallbackResponse.data,
        message: 'Talleres obtenidos exitosamente',
      };
    } catch (fallbackError: any) {
      console.error('Error getting workshops (fallback):', fallbackError.response?.data || fallbackError);
      return {
        success: false,
        data: [],
        message: 'Error al obtener los talleres',
      };
    }
  }
});
