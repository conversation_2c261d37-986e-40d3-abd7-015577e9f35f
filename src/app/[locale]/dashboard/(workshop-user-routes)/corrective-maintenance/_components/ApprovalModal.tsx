'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';
import { Quotation } from '../types';

interface ApprovalModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  quotation?: Quotation;
}

export default function ApprovalModal({ isOpen, onClose, onSuccess, quotation }: ApprovalModalProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      // TODO: Implement approval processing logic
      toast({
        title: 'Éxito',
        description: 'Aprobación procesada exitosamente',
      });
      onSuccess();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Error al procesar la aprobación',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Procesar Aprobación</DialogTitle>
        </DialogHeader>

        <div className="py-4">
          <p className="text-gray-600">
            Funcionalidad de aprobación en desarrollo...
          </p>
          {quotation && (
            <div className="mt-4">
              <h4 className="font-medium mb-2">Cotización #{quotation.quotationNumber}</h4>
              <p className="text-sm text-gray-600">
                Total: ${quotation.totalCost}
              </p>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={onClose}>
            Cancelar
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Procesar Aprobación
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
