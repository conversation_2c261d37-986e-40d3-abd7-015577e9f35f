'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';
import { CorrectiveService } from '../types';

interface ServiceProgressModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  serviceId: string;
  service?: CorrectiveService;
}

export default function ServiceProgressModal({ 
  isOpen, 
  onClose, 
  onSuccess, 
  serviceId, 
  service 
}: ServiceProgressModalProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      // TODO: Implement service progress update logic
      toast({
        title: 'Éxito',
        description: 'Progreso del servicio actualizado exitosamente',
      });
      onSuccess();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Error al actualizar el progreso del servicio',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Actualizar Progreso del Servicio</DialogTitle>
        </DialogHeader>

        <div className="py-4">
          <p className="text-gray-600">
            Funcionalidad de actualización de progreso en desarrollo...
          </p>
          {service && (
            <div className="mt-4">
              <h4 className="font-medium mb-2">{service.serviceName}</h4>
              <p className="text-sm text-gray-600">
                Estado actual: {service.status}
              </p>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={onClose}>
            Cancelar
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Actualizar Progreso
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
