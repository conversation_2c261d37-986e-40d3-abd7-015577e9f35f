'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';
import { CreateOrderRequest } from '../types';
import { createOrder } from '../_actions/createOrder';
import { getVehicles } from '../_actions/getVehicles';
import { getWorkshops } from '../_actions/getWorkshops';

const createOrderSchema = z.object({
  stockId: z.string().min(1, 'Selecciona un vehículo'),
  associateId: z.string().min(1, 'ID del asociado es requerido'),
  workshopId: z.string().min(1, 'Selecciona un taller'),
  type: z.enum(['customer-initiated', 'preventive-detected']),
  failureType: z.enum(['known', 'unknown']),
  arrivalMethod: z.enum(['driving', 'tow-truck']),
  customerDescription: z.string().min(10, 'La descripción debe tener al menos 10 caracteres'),
  canVehicleDrive: z.boolean(),
  needsTowTruck: z.boolean(),
  approvalType: z.enum(['fleet', 'customer']),
});

type CreateOrderFormData = z.infer<typeof createOrderSchema>;

interface CreateOrderModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface Vehicle {
  _id: string;
  brand: string;
  model: string;
  carNumber: string;
  carPlates: { plates: string };
}

interface Workshop {
  _id: string;
  name: string;
}

export default function CreateOrderModal({ isOpen, onClose, onSuccess }: CreateOrderModalProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [workshops, setWorkshops] = useState<Workshop[]>([]);
  const [loadingData, setLoadingData] = useState(false);

  const form = useForm<CreateOrderFormData>({
    resolver: zodResolver(createOrderSchema),
    defaultValues: {
      type: 'customer-initiated',
      failureType: 'unknown',
      arrivalMethod: 'driving',
      canVehicleDrive: true,
      needsTowTruck: false,
      approvalType: 'fleet',
    },
  });

  // Load vehicles and workshops when modal opens
  useEffect(() => {
    if (isOpen) {
      loadInitialData();
    }
  }, [isOpen]);

  const loadInitialData = async () => {
    setLoadingData(true);
    try {
      const [vehiclesResponse, workshopsResponse] = await Promise.all([
        getVehicles(),
        getWorkshops(),
      ]);

      if (vehiclesResponse?.success) {
        setVehicles(vehiclesResponse.data);
      }

      if (workshopsResponse?.success) {
        setWorkshops(workshopsResponse.data);
      }
    } catch (error) {
      console.error('Error loading initial data:', error);
      toast({
        title: 'Error',
        description: 'Error al cargar los datos iniciales',
        variant: 'destructive',
      });
    } finally {
      setLoadingData(false);
    }
  };

  const onSubmit = async (data: CreateOrderFormData) => {
    setIsSubmitting(true);
    try {
      const response = await createOrder(data);
      
      if (response.success) {
        toast({
          title: 'Éxito',
          description: 'Orden de mantenimiento correctivo creada exitosamente',
        });
        form.reset();
        onSuccess();
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Error al crear la orden',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error creating order:', error);
      toast({
        title: 'Error',
        description: 'Error inesperado al crear la orden',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    form.reset();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Nueva Orden de Mantenimiento Correctivo</DialogTitle>
        </DialogHeader>

        {loadingData ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Cargando datos...</span>
          </div>
        ) : (
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Vehicle Selection */}
              <div className="space-y-2">
                <Label htmlFor="stockId">Vehículo *</Label>
                <Select
                  value={form.watch('stockId')}
                  onValueChange={(value) => form.setValue('stockId', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecciona un vehículo" />
                  </SelectTrigger>
                  <SelectContent>
                    {vehicles.map((vehicle) => (
                      <SelectItem key={vehicle._id} value={vehicle._id}>
                        {vehicle.brand} {vehicle.model} - {vehicle.carNumber} ({vehicle.carPlates?.plates})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {form.formState.errors.stockId && (
                  <p className="text-sm text-red-500">{form.formState.errors.stockId.message}</p>
                )}
              </div>

              {/* Workshop Selection */}
              <div className="space-y-2">
                <Label htmlFor="workshopId">Taller *</Label>
                <Select
                  value={form.watch('workshopId')}
                  onValueChange={(value) => form.setValue('workshopId', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecciona un taller" />
                  </SelectTrigger>
                  <SelectContent>
                    {workshops.map((workshop) => (
                      <SelectItem key={workshop._id} value={workshop._id}>
                        {workshop.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {form.formState.errors.workshopId && (
                  <p className="text-sm text-red-500">{form.formState.errors.workshopId.message}</p>
                )}
              </div>

              {/* Associate ID */}
              <div className="space-y-2">
                <Label htmlFor="associateId">ID del Asociado *</Label>
                <Input
                  {...form.register('associateId')}
                  placeholder="ID del cliente/asociado"
                />
                {form.formState.errors.associateId && (
                  <p className="text-sm text-red-500">{form.formState.errors.associateId.message}</p>
                )}
              </div>

              {/* Order Type */}
              <div className="space-y-2">
                <Label htmlFor="type">Tipo de Orden *</Label>
                <Select
                  value={form.watch('type')}
                  onValueChange={(value: 'customer-initiated' | 'preventive-detected') => 
                    form.setValue('type', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="customer-initiated">Iniciado por Cliente</SelectItem>
                    <SelectItem value="preventive-detected">Detectado en Mantenimiento Preventivo</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Failure Type */}
              <div className="space-y-2">
                <Label htmlFor="failureType">Tipo de Falla *</Label>
                <Select
                  value={form.watch('failureType')}
                  onValueChange={(value: 'known' | 'unknown') => form.setValue('failureType', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="known">Falla Conocida</SelectItem>
                    <SelectItem value="unknown">Requiere Diagnóstico</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Arrival Method */}
              <div className="space-y-2">
                <Label htmlFor="arrivalMethod">Método de Llegada *</Label>
                <Select
                  value={form.watch('arrivalMethod')}
                  onValueChange={(value: 'driving' | 'tow-truck') => {
                    form.setValue('arrivalMethod', value);
                    form.setValue('canVehicleDrive', value === 'driving');
                    form.setValue('needsTowTruck', value === 'tow-truck');
                  }}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="driving">Llega Rodando</SelectItem>
                    <SelectItem value="tow-truck">Requiere Grúa</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Approval Type */}
              <div className="space-y-2">
                <Label htmlFor="approvalType">Tipo de Aprobación *</Label>
                <Select
                  value={form.watch('approvalType')}
                  onValueChange={(value: 'fleet' | 'customer') => form.setValue('approvalType', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="fleet">Aprobación de Flotillas</SelectItem>
                    <SelectItem value="customer">Aprobación del Cliente</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Customer Description */}
            <div className="space-y-2">
              <Label htmlFor="customerDescription">Descripción del Problema *</Label>
              <Textarea
                {...form.register('customerDescription')}
                placeholder="Describe detalladamente el problema reportado por el cliente..."
                rows={4}
              />
              {form.formState.errors.customerDescription && (
                <p className="text-sm text-red-500">{form.formState.errors.customerDescription.message}</p>
              )}
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancelar
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Crear Orden
              </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
}
