'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';
import { CorrectiveMaintenanceOrder } from '../types';

interface QuotationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  order: CorrectiveMaintenanceOrder;
}

export default function QuotationModal({ isOpen, onClose, onSuccess, order }: QuotationModalProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      // TODO: Implement quotation creation logic
      toast({
        title: 'Éxito',
        description: 'Cotización creada exitosamente',
      });
      onSuccess();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Error al crear la cotización',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Crear Cotización</DialogTitle>
        </DialogHeader>

        <div className="py-4">
          <p className="text-gray-600">
            Funcionalidad de cotización en desarrollo...
          </p>
          {order.services && (
            <div className="mt-4">
              <h4 className="font-medium mb-2">Servicios a cotizar:</h4>
              <ul className="space-y-1">
                {order.services.map((service) => (
                  <li key={service._id} className="text-sm">
                    • {service.serviceName}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={onClose}>
            Cancelar
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Crear Cotización
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
