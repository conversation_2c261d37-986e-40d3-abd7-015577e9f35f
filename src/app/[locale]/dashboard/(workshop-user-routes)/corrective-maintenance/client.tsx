'use client';

import { useState } from 'react';
import { useRouter, useSearchParams, usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, Filter, Plus } from 'lucide-react';
import { CorrectiveMaintenanceOrder, ApiResponse, OrderFilters, ORDER_STATUS_LABELS } from './types';
import OrdersTable from './_components/OrdersTable';
import CreateOrderModal from './_components/CreateOrderModal';

interface CorrectiveMaintenanceClientProps {
  initialData: ApiResponse<CorrectiveMaintenanceOrder[]> | null;
  initialFilters: {
    page?: number;
    limit?: number;
    status?: string;
    workshopId?: string;
    search?: string;
  };
}

export default function CorrectiveMaintenanceClient({ 
  initialData, 
  initialFilters 
}: CorrectiveMaintenanceClientProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  
  const [filters, setFilters] = useState<OrderFilters>({
    status: initialFilters.status,
    search: initialFilters.search,
  });
  
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  const updateSearchParams = (newFilters: Partial<OrderFilters>) => {
    const params = new URLSearchParams(searchParams.toString());
    
    // Update or remove parameters
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value && value !== '') {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });
    
    // Reset to page 1 when filters change
    if (Object.keys(newFilters).some(key => key !== 'page')) {
      params.set('page', '1');
    }
    
    router.push(`${pathname}?${params.toString()}`);
  };

  const handleFilterChange = (key: keyof OrderFilters, value: string) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    updateSearchParams(newFilters);
  };

  const handleSearch = (searchTerm: string) => {
    handleFilterChange('search', searchTerm);
  };

  const clearFilters = () => {
    setFilters({});
    router.push(pathname);
  };

  const handlePageChange = (page: number) => {
    updateSearchParams({ page: page.toString() });
  };

  const handleOrderCreated = () => {
    setIsCreateModalOpen(false);
    // Refresh the page to show the new order
    router.refresh();
  };

  if (!initialData) {
    return (
      <div className="flex justify-center items-center py-10">
        <p className="text-red-500">Error al cargar las órdenes de mantenimiento correctivo</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters and Actions */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center flex-1">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Buscar por número de orden, vehículo..."
              value={filters.search || ''}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Status Filter */}
          <Select
            value={filters.status || ''}
            onValueChange={(value) => handleFilterChange('status', value)}
          >
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Filtrar por estado" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Todos los estados</SelectItem>
              {Object.entries(ORDER_STATUS_LABELS).map(([value, label]) => (
                <SelectItem key={value} value={value}>
                  {label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Clear Filters */}
          {(filters.status || filters.search) && (
            <Button variant="outline" onClick={clearFilters}>
              <Filter className="h-4 w-4 mr-2" />
              Limpiar filtros
            </Button>
          )}
        </div>

        {/* Create Order Button */}
        <Button onClick={() => setIsCreateModalOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Nueva Orden
        </Button>
      </div>

      {/* Orders Table */}
      <OrdersTable
        data={initialData.data}
        pagination={initialData.pagination}
        onPageChange={handlePageChange}
      />

      {/* Create Order Modal */}
      <CreateOrderModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSuccess={handleOrderCreated}
      />
    </div>
  );
}
