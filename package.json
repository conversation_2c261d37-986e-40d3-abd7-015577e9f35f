{"name": "vendor-platform", "version": "0.1.41", "private": true, "scripts": {"dev": "next dev -p 5001  ", "build": "next build", "start": "next start", "cypress:start": "cypress open", "some": "ts-node src/constants/some.js", "e2e": "cypress --e2e", "lint": "eslint . --ext .ts,.tsx"}, "dependencies": {"@chakra-ui/next-js": "2.2.0", "@chakra-ui/react": "2.7.0", "@emotion/react": "11.11.1", "@emotion/styled": "11.11.0", "@faker-js/faker": "^8.4.1", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.1.4", "@react-pdf/renderer": "3.1.12", "@reduxjs/toolkit": "1.9.5", "@tanstack/react-query": "^5.74.4", "@tanstack/react-table": "^8.17.3", "@types/node": "20.2.5", "@types/react": "^18.3.3", "@types/react-dom": "18.2.4", "@types/yup": "^0.32.0", "animate.css": "4.1.1", "autoprefixer": "10.4.14", "axios": "1.4.0", "chart.js": "4.4.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^0.2.1", "cookie": "^0.6.0", "cookies-next": "^4.2.1", "cypress": "12.14.0", "date-fns": "^2.30.0", "date-fns-tz": "2.0.0", "dayjs": "1.11.9", "eslint": "8.41.0", "eslint-config-next": "14.2.5", "form-data": "^4.0.0", "formik": "^2.4.6", "framer-motion": "10.12.16", "jszip": "^3.10.1", "lodash": "^4.17.21", "lucide-react": "^0.378.0", "luxon": "^3.5.0", "moment": "2.29.4", "next": "^14.2.8", "next-auth": "^4.24.5", "next-international": "^1.2.4", "next-themes": "0.3.0", "nodemailer": "^6.9.15", "numero-a-letras": "1.0.6", "nuqs": "^2.2.3", "postcss": "8.4.24", "prom-client": "^15.1.2", "react": "18.2.0", "react-big-calendar": "^1.17.1", "react-calendar": "^5.0.0", "react-chartjs-2": "5.2.0", "react-date-picker": "^11.0.0", "react-day-picker": "^8.10.1", "react-dom": "18.2.0", "react-hook-form": "^7.53.2", "react-icons": "^4.12.0", "react-redux": "8.1.1", "react-select": "5.7.3", "react-signature-canvas": "^1.0.6", "react-virtuoso": "^4.10.1", "react-window": "^1.8.10", "react-zoom-pan-pinch": "3.1.0", "recharts": "^2.13.0-alpha.4", "sonner": "^1.4.41", "sweetalert2": "11.10.0", "sweetalert2-react-content": "^5.1.0", "tailwind-merge": "^2.3.0", "tailwindcss": "3.3.2", "tailwindcss-animate": "^1.0.7", "typescript": "^5.4.5", "uuid": "^10.0.0", "xlsx": "^0.18.5", "yup": "^0.32.11", "zod": "^3.23.8", "zustand": "4.4.6"}, "devDependencies": {"@next/eslint-plugin-next": "13.4.5", "@types/luxon": "^3.4.2", "@types/nodemailer": "^6.4.15", "@types/react-big-calendar": "^1.16.1", "@types/react-signature-canvas": "^1.0.5", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "5.59.9", "@typescript-eslint/parser": "5.59.9", "encoding": "^0.1.13", "eslint-config-airbnb": "19.0.4", "eslint-config-airbnb-base": "15.0.0", "eslint-config-airbnb-typescript": "17.0.0", "eslint-config-prettier": "8.8.0", "eslint-plugin-cypress": "2.13.3", "eslint-plugin-prettier": "4.2.1", "husky": "8.0.3", "lint-staged": "13.2.2", "prettier": "2.8.8"}, "engines": {"node": ">=18.0.0"}}